<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="description" content="AI Plant Identifier - Identify plants using your camera. Get detailed information about plant species, care instructions, and build your plant collection.">
  <meta name="keywords" content="plant identification, AI, plant care, botany, gardening, plant species">
  <meta name="author" content="Anas <PERSON>">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="AI Plant Identifier">
  <meta property="og:description" content="Identify plants using AI technology. Get detailed plant information and care instructions.">
  <meta property="og:image" content="icons/Icon-512.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="AI Plant Identifier">
  <meta property="twitter:description" content="Identify plants using AI technology. Get detailed plant information and care instructions.">
  <meta property="twitter:image" content="icons/Icon-512.png">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="AI Plant Identifier">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="152x152" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="180x180" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" sizes="512x512" href="icons/Icon-512.png">

  <title>AI Plant Identifier - Identify Plants with AI</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
