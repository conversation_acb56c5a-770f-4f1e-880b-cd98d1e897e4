                        -HJ:\flutter\src\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=J:\Android\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=J:\Android\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=J:\Android\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DC<PERSON><PERSON>_MAKE_PROGRAM=J:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\cxx\RelWithDebInfo\2l1a1nu2\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\cxx\RelWithDebInfo\2l1a1nu2\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BI:\ALL THE PROJECTS\Neuroxes\PlantApp\build\.cxx\RelWithDebInfo\2l1a1nu2\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2