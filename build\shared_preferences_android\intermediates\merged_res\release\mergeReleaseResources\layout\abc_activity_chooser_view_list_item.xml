<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:id="@+id/list_item"
              android:layout_width="match_parent"
              android:layout_height="?attr/dropdownListPreferredItemHeight"
              android:paddingLeft="16dip"
              android:paddingRight="16dip"
              android:minWidth="196dip"
              android:orientation="vertical"
              android:background="?attr/selectableItemBackground"  >

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:duplicateParentState="true" >

        <ImageView
                android:id="@+id/icon"
                android:layout_width="32dip"
                android:layout_height="32dip"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="8dip"
                android:duplicateParentState="true"/>

        <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textAppearance="?attr/textAppearanceLargePopupMenu"
                android:duplicateParentState="true"
                android:singleLine="true"
                android:ellipsize="marquee"
                android:fadingEdge="horizontal"/>

    </LinearLayout>

</LinearLayout>