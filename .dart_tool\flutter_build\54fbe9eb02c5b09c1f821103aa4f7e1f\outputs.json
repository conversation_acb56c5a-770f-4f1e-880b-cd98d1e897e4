["I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders/ink_sparkle.frag", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so"]