{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\.cxx\\RelWithDebInfo\\2l1a1nu2\\arm64-v8a", "soFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\2l1a1nu2\\obj\\arm64-v8a", "soRepublishFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cmake\\release\\obj\\arm64-v8a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-Wno-dev", "--no-warn-unused-cli"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\.cxx", "intermediatesBaseFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates", "intermediatesFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\android\\app", "moduleBuildFile": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\android\\app\\build.gradle.kts", "makeFile": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "J:\\Android\\ndk\\27.0.12077973", "ndkFolderBeforeSymLinking": "J:\\Android\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "J:\\Android\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "J:\\Android\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "J:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "J:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "J:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "J:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "J:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\android", "sdkFolder": "J:\\Android", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\.cxx\\RelWithDebInfo\\2l1a1nu2\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "2l1a1nu2y28c1j1d3c33q6p4p1o2x29415w6r2w4u3u6i532q441v3d1a4b", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.3.\n#   - $NDK is the path to NDK 27.0.12077973.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HJ:/flutter/src/flutter/packages/flutter_tools/gradle/src/main/scripts\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-<PERSON><PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:/ALL THE PROJECTS/Neuroxes/PlantApp/build/app/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:/ALL THE PROJECTS/Neuroxes/PlantApp/build/app/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-BI:/ALL THE PROJECTS/Neuroxes/PlantApp/build/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-Wno-dev\n--no-warn-unused-cli", "configurationArguments": ["-HJ:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=J:\\Android\\ndk\\27.0.12077973", "-DCMAKE_ANDROID_NDK=J:\\Android\\ndk\\27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=J:\\Android\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\2l1a1nu2\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\2l1a1nu2\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-BI:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\.cxx\\RelWithDebInfo\\2l1a1nu2\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-Wno-dev", "--no-warn-unused-cli"], "intermediatesParentFolder": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\2l1a1nu2"}