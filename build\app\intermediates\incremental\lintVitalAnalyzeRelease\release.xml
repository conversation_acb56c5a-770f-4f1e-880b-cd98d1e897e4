<variant
    name="release"
    package="com.example.ai_plant_identifier"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;J:\flutter\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da88b15af196281806ee75cad78ce8f0\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\tmp\kotlin-classes\release;I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\kotlinToolingMetadata;I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.ai_plant_identifier"
      generatedSourceFolders="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da88b15af196281806ee75cad78ce8f0\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
