{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "I:/ALL THE PROJECTS/Neuroxes/PlantApp/build/.cxx/RelWithDebInfo/2l1a1nu2/arm64-v8a", "source": "J:/flutter/src/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}