[{"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_bar_title_item.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_bar_title_item.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_search_view.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_search_view.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/select_dialog_singlechoice_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_cascading_menu_item_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_list_fragment.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_list_fragment.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_activity_chooser_view.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_activity_chooser_view.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_information_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_information_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/custom_dialog.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_screen_simple.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_screen_simple.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_list_menu_item_checkbox.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_tooltip.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_tooltip.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/support_simple_spinner_dropdown_item.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_popup_menu_item_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_dialog_edittext.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_dialog_edittext.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_alert_dialog_button_bar_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_select_dialog_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_select_dialog_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/select_dialog_item_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/select_dialog_item_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_alert_dialog_title_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_bar_up_container.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_bar_up_container.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_widget_seekbar_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_widget_seekbar_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_popup_menu_header_item_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_alert_dialog_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_alert_dialog_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_category.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_category.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_expanded_menu_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_expanded_menu_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_recyclerview.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_recyclerview.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_widget_seekbar.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_widget_seekbar.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_mode_close_item_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_widget_switch.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_widget_switch.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_screen_toolbar.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_screen_toolbar.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_widget_checkbox.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_widget_checkbox.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_category_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_category_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/image_frame.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/image_frame.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_menu_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_menu_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_information.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_information.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_dropdown.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_dropdown.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_dialog_title_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_dialog_title_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_mode_bar.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_mode_bar.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/expand_button.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/expand_button.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_list_menu_item_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_list_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_list_menu_item_radio.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_list_menu_item_radio.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_activity_chooser_view_list_item.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_activity_chooser_view_list_item.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_screen_content_include.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_screen_content_include.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_action_menu_item_layout.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_action_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/select_dialog_multichoice_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/select_dialog_multichoice_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_widget_switch_compat.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_widget_switch_compat.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/preference_dropdown_material.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-preference-1.2.1-6:/layout/preference_dropdown_material.xml"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/layout/abc_list_menu_item_icon.xml", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-appcompat-1.1.0-19:/layout/abc_list_menu_item_icon.xml"}]