<lint-module
    format="1"
    dir="I:\ALL THE PROJECTS\Neuroxes\PlantApp\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="I:\ALL THE PROJECTS\Neuroxes\PlantApp\build\app"
    bootClassPath="J:\Android\platforms\android-35\android.jar;J:\Android\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
